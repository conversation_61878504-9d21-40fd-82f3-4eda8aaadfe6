/**
 *==================================================================================================
 * Project: Utils
 * Module: GstUtility.cpp
 * Module Short Description: Gst encoder/decoder input params or options. Some utils functions to
 *                           initialize gstreamer api and log while gstreamer running.
 * Author: Trung-Ng (<EMAIL>)
 * Date: 20/05/2020 16:41
 * Organization: Viettel Aerospace Institude - Viettel Group
 *
 * (c) VTX 2020. All Rights reserved.
 * =================================================================================================
 */

#include "GstUtility.h"

#include <gst/gst.h>
#include <stdint.h>
#include <stdio.h>

namespace Utils {
    namespace Gst {
        /**
         * @brief gst_debug_level_str
         * @param level
         * @return
         */
        inline const char* gst_debug_level_str( GstDebugLevel t_level )
        {
            switch (t_level)
            {
                case GST_LEVEL_NONE:	return "GST_LEVEL_NONE   ";
                case GST_LEVEL_ERROR:	return "GST_LEVEL_ERROR  ";
                case GST_LEVEL_WARNING:	return "GST_LEVEL_WARNING";
                case GST_LEVEL_INFO:	return "GST_LEVEL_INFO   ";
                case GST_LEVEL_DEBUG:	return "GST_LEVEL_DEBUG  ";
                case GST_LEVEL_LOG:		return "GST_LEVEL_LOG    ";
                case GST_LEVEL_FIXME:	return "GST_LEVEL_FIXME  ";
        #ifdef GST_LEVEL_TRACE
                case GST_LEVEL_TRACE:	return "GST_LEVEL_TRACE  ";
        #endif
                case GST_LEVEL_MEMDUMP:	return "GST_LEVEL_MEMDUMP";
                    default:				return "<unknown>        ";
            }
        }

        #define SEP "              "

        /**
         * @brief rilog_debug_function
         * @param category
         * @param level
         * @param file
         * @param function
         * @param line
         * @param object
         * @param message
         * @param data
         */
        void rilog_debug_function(GstDebugCategory* t_category, GstDebugLevel t_level,
                                  const gchar* t_file, const char* t_function,
                                  gint t_line, GObject* t_object, GstDebugMessage* message,
                                  gpointer t_data)
        {
            if( t_level > GST_LEVEL_WARNING /*GST_LEVEL_INFO*/ )
                return;

            //gchar* name = NULL;
            //if( object != NULL )
            //	g_object_get(object, "name", &name, NULL);

            const char* typeName  = " ";
            const char* className = " ";

            if( t_object != NULL )
            {
                typeName  = G_OBJECT_TYPE_NAME(t_object);
                className = G_OBJECT_CLASS_NAME(t_object);
            }

            printf(LOG_GSTREAMER "%s %s %s\n" SEP "%s:%i  %s\n" SEP "%s\n",
                    gst_debug_level_str(t_level), typeName,
                    gst_debug_category_get_name(t_category), t_file, t_line, t_function,
                        gst_debug_message_get(message));

        }

        /**
         * @brief gstreamerInit: Initialize gstreamer API
         * @return
         */
        bool gstreamerInit()
        {
            static bool initFlag = false;
            if(initFlag == false)
            {
                int argc = 0;
                //char* argv[] = { "none" };

                if( !gst_init_check(&argc, NULL, NULL) )
                {
                    printf(LOG_GSTREAMER "failed to initialize gstreamer library with gst_init()\n");
                    return false;
                }

                uint32_t ver[] = { 0, 0, 0, 0 };
                gst_version( &ver[0], &ver[1], &ver[2], &ver[3] );

                printf(LOG_GSTREAMER "initialized gstreamer, version %u.%u.%u.%u\n", ver[0], ver[1], ver[2], ver[3]);


                // debugging
                gst_debug_remove_log_function(gst_debug_log_default);

                if( true )
                {
                    gst_debug_add_log_function(rilog_debug_function, NULL, NULL);

                    gst_debug_set_active(true);
                    gst_debug_set_colored(false);
                }
                initFlag = true;
            }
            return true;
        }

        //---------------------------------------------------------------------------------------------
        /**
         * @brief gst_print_one_tag
         * @param list
         * @param tag
         * @param user_data
         */
        static void gst_print_one_tag(const GstTagList * t_list, const gchar * t_tag, gpointer t_user_data)
        {
          int i, num;

          num = gst_tag_list_get_tag_size (t_list, t_tag);
          for (i = 0; i < num; ++i) {
            const GValue *val;

            /* Note: when looking for specific tags, use the gst_tag_list_get_xyz() API,
             * we only use the GValue approach here because it is more generic */
            val = gst_tag_list_get_value_index (t_list, t_tag, i);
            if (G_VALUE_HOLDS_STRING (val)) {
              printf("\t%20s : %s\n", t_tag, g_value_get_string (val));
            } else if (G_VALUE_HOLDS_UINT (val)) {
              printf("\t%20s : %u\n", t_tag, g_value_get_uint (val));
            } else if (G_VALUE_HOLDS_DOUBLE (val)) {
              printf("\t%20s : %g\n", t_tag, g_value_get_double (val));
            } else if (G_VALUE_HOLDS_BOOLEAN (val)) {
              printf("\t%20s : %s\n", t_tag,
                  (g_value_get_boolean (val)) ? "true" : "false");
            } else if (GST_VALUE_HOLDS_BUFFER (val)) {
              printf("\t%20s : buffer of size %u\n", t_tag, /*buffer_size*/0);
            } else {
              printf("\t%20s : tag of type '%s'\n", t_tag, G_VALUE_TYPE_NAME (val));
            }
          }
        }

        /**
         * @brief gst_stream_status_string
         * @param status
         * @return
         */
        static const char* gst_stream_status_string( GstStreamStatusType t_status )
        {
            switch (t_status)
            {
                case GST_STREAM_STATUS_TYPE_CREATE:	return "CREATE";
                case GST_STREAM_STATUS_TYPE_ENTER:		return "ENTER";
                case GST_STREAM_STATUS_TYPE_LEAVE:		return "LEAVE";
                case GST_STREAM_STATUS_TYPE_DESTROY:	return "DESTROY";
                case GST_STREAM_STATUS_TYPE_START:		return "START";
                case GST_STREAM_STATUS_TYPE_PAUSE:		return "PAUSE";
                case GST_STREAM_STATUS_TYPE_STOP:		return "STOP";
                default:							return "UNKNOWN";
            }
        }

        // gst_message_print
        gboolean gst_message_print(GstBus* t_bus, GstMessage* t_message, gpointer t_user_data)
        {

            switch (GST_MESSAGE_TYPE (t_message))
            {
                case GST_MESSAGE_ERROR:
                {
                    GError *err = NULL;
                    gchar *dbg_info = NULL;

                    gst_message_parse_error (t_message, &err, &dbg_info);
                    printf(LOG_GSTREAMER "gstreamer %s ERROR %s\n", GST_OBJECT_NAME (t_message->src), err->message);
                        printf(LOG_GSTREAMER "gstreamer Debugging info: %s\n", (dbg_info) ? dbg_info : "none");

                    g_error_free(err);
                        g_free(dbg_info);
                        break;
                }
                case GST_MESSAGE_EOS:
                {
                    printf(LOG_GSTREAMER "gstreamer %s recieved EOS signal...\n", GST_OBJECT_NAME(t_message->src));
                    break;
                }
                case GST_MESSAGE_STATE_CHANGED:
                {
                    GstState old_state, new_state;

                    gst_message_parse_state_changed(t_message, &old_state, &new_state, NULL);

                    printf(LOG_GSTREAMER "gstreamer changed state from %s to %s ==> %s\n",
                                    gst_element_state_get_name(old_state),
                                    gst_element_state_get_name(new_state),
                                     GST_OBJECT_NAME(t_message->src));
                    break;
                }
                case GST_MESSAGE_STREAM_STATUS:
                {
                    GstStreamStatusType streamStatus;
                    gst_message_parse_stream_status(t_message, &streamStatus, NULL);

                    printf(LOG_GSTREAMER "gstreamer stream status %s ==> %s\n",
                                    gst_stream_status_string(streamStatus),
                                    GST_OBJECT_NAME(t_message->src));
                    break;
                }
                case GST_MESSAGE_TAG:
                {
                    GstTagList *tags = NULL;

                    gst_message_parse_tag(t_message, &tags);

        #ifdef gst_tag_list_to_string
                    gchar* txt = gst_tag_list_to_string(tags);
        #else
                    gchar* txt = "missing gst_tag_list_to_string()";
        #endif

                    if( txt != NULL )
                    {
                        printf(LOG_GSTREAMER "gstreamer %s %s\n", GST_OBJECT_NAME(t_message->src), txt);
        #ifdef gst_tag_list_to_string
                        g_free(txt);
        #endif
                    }

                    //gst_tag_list_foreach(tags, gst_print_one_tag, NULL);
                    if( tags != NULL )
                        gst_tag_list_free(tags);
                    break;
                }
                default:
                {
                    printf(LOG_GSTREAMER "gstreamer msg %s ==> %s\n", gst_message_type_get_name(GST_MESSAGE_TYPE(t_message)), GST_OBJECT_NAME(t_message->src));
                    break;
                }
            }

            return TRUE;
        }


        GstDiscovererVideoInfo* findVideoStreamInfo(GstDiscovererStreamInfo* t_info)
        {
            if (!t_info)
                    return NULL;

            //printf("stream type -- %s\n", gst_discoverer_stream_info_get_stream_type_nick(info));

            if (GST_IS_DISCOVERER_VIDEO_INFO(t_info))
            {
                return GST_DISCOVERER_VIDEO_INFO(t_info);
            }
            else if(GST_IS_DISCOVERER_CONTAINER_INFO(t_info))
            {
                GstDiscovererContainerInfo* containerInfo = GST_DISCOVERER_CONTAINER_INFO(t_info);

                if (!containerInfo)
                    return NULL;

                GList* containerStreams = gst_discoverer_container_info_get_streams(containerInfo);

                for (GList* n=containerStreams; n; n = n->next)
                {
                    GstDiscovererVideoInfo* videoStream = findVideoStreamInfo(GST_DISCOVERER_STREAM_INFO(n->data));

                    if (videoStream != NULL)
                        return videoStream;
                }
            }

            return findVideoStreamInfo(gst_discoverer_stream_info_get_next(t_info));
        }
    }
}
