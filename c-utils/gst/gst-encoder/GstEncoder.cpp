/**
 *========================================================================
 * Project: Utils
 * Module: GstEncoder
 * Module Short Description:Gstreamer encoder module supports udp streaming
 *                          or video recording features.
 * Author: Trung-Ng (<EMAIL>)
 * Date: 11/06/2020 09:58
 * Organization: Viettel Aerospace Institude - Viettel Group
 *
 * (c) VTX 2020. All Rights reserved.
 * =======================================================================
 */

#include "GstEncoder.h"
#include "../../logging/LogApis.h"
#include "../../helper/FileHelper.h"
#include "opencv2/opencv.hpp"

namespace Utils
{
    /**
     * @brief The GstEncoderImpl class: The implementation of all GstEncoder api
     */
    class GstEncoderImpl : public GstEncoder
    {

        public:
            /** Constructor */
            GstEncoderImpl(const Option& t_encoderOption);

            /** Destructor */
            ~GstEncoderImpl();

            /**
             * @brief openStream: The implementation of openStream
             * @return
             */
            bool openStream() override;

            /**
             * @brief closeStream: The implementation of closeStream
             * @return
             */
            bool closeStream() override;

            /**
             * @brief isRunning: The implementation of isRunning
             * @return
             */
            bool isRunning() override;

            /**
             * @brief encodeFrame: The implementation of encodeFrame
             * @param t_imgData
             * @return
             */
            bool encodeFrame(void *t_imgData, size_t t_size) override;

            /**
             * @brief GstEncoderImpl::pause
             * @param t_pause
             */
            void pause(bool t_pause) override;
        //protected:
            /**
             * @brief createCaps: Create caps filter input data for appsrc element.
             */
            void createCaps();

            /**
             * @brief createPipeline: Create gstreamer pipeline in string to streaming image
             *                        from camera
             */
            void createPipeline();

            /**
             * @brief init: create gstreamer pipeline elements and setup event callback.
             * @return
             */
            bool init();

            //---- Gst events
            /**
             * @brief onNeedData: The event notify that gstreamer pipeline need to feed new data.
             *
             * @param t_appSrc: AppSrc element
             * @param t_size
             * @param t_userData
             * @return
             */
            static void onNeedData(GstAppSrc* t_appSrc, uint32_t t_size, void* t_userData);

            /**
             * @brief onEnoughData: The event fired when gstreamer is encoding data. Do not need to
             *                      feed new data into pipeline.
             *
             * @param t_appSrc
             * @param t_userData
             * @return
             */
            static void onEnoughData(GstAppSrc* t_appSrc, void* t_userData);

        private:
            Option                  m_encoderOption;
            int                     m_numFrame = 0;
            //----Gst
            GstPipeline*            m_gstPipeline;
            GstAppSrc*              m_appSrc;
            GstCaps*                m_appSrcCaps;
            std::string             m_gstPipelineStr;
            std::string             m_appSrcCapsStr;
            bool                    m_needData;
            bool                    m_isStreaming;
            std::string             m_appSrcName;
            //---Sync
            std::mutex              m_needDataMutex;
            std::condition_variable m_waitAppsrcNeedData;
    };

    //----------------------------------------------------------------------------------------------

    // Constructor
    GstEncoderImpl::GstEncoderImpl(const Option& t_encoderOption)
        : m_encoderOption(t_encoderOption)
    {
        m_gstPipeline    = NULL;
        m_appSrc         = NULL;
        m_appSrcCaps     = NULL;
        m_gstPipelineStr = "";
        m_appSrcCapsStr  = "";
        m_needData       = false;
        m_isStreaming    = false;
        m_appSrcName     = "myappsrc";
        if(!t_encoderOption.appSrcName.empty())
            m_appSrcName = t_encoderOption.appSrcName;
    }

    // Destructor
    GstEncoderImpl::~GstEncoderImpl()
    {
        closeStream();

        if (m_gstPipeline != NULL)
        {
            gst_object_unref(m_gstPipeline);
            m_gstPipeline = NULL;
        }

        if (m_appSrc != NULL)
        {
            gst_object_unref(m_appSrc);
            m_appSrc = NULL;
        }
    }

    // openStream
    bool GstEncoderImpl::openStream()
    {
        if (m_isStreaming)
            return true;

        logInfo(GST_ENCODER_LOG, "Openstream: transitioning pipeline to GST_STATE_PLAYING");
        const GstStateChangeReturn result = gst_element_set_state(GST_ELEMENT(m_gstPipeline),
                                                                                 GST_STATE_PLAYING);
        if (result != GST_STATE_CHANGE_SUCCESS)
        {
            logDanger(GST_ENCODER_LOG, "failed to set pipeline state to PLAYING (error %u)",
                                                                                            result);
            return false;
        }

        usleep(100*1000);
        m_isStreaming = true;
        return true;
    }

    // closeStream
    bool GstEncoderImpl::closeStream()
    {
        if (!m_isStreaming)
            return true;
        logInfo(GST_ENCODER_LOG, "closeStream -- shutting down pipeline, sending EOS");
        GstFlowReturn eosResult = gst_app_src_end_of_stream(m_appSrc);

        if (eosResult != 0)
        {
            logDanger(GST_ENCODER_LOG, "closeStream -- failed sending appsrc EOS (result %u)",
                      eosResult);
            return false;
        }

        sleep(1);
        logInfo(GST_ENCODER_LOG, "closeStream -- transitioning pipeline to GST_STATE_NULL");
        const GstStateChangeReturn result = gst_element_set_state(GST_ELEMENT(m_gstPipeline),
                                                                  GST_STATE_NULL);
        if (result != GST_STATE_CHANGE_SUCCESS)
        {
            logDanger(GST_ENCODER_LOG, "closeStream -- failed to set pipeline state to NULL "
                                                                              "(error %u)", result);
            return false;
        }

        sleep(1);
        m_isStreaming = false;
        logInfo(GST_ENCODER_LOG, "closeStream -- pipeline stopped!");
        return true;
    }

    // isRunning
    bool GstEncoderImpl::isRunning()
    {
        return m_isStreaming;
    }

    // encodeFrame
    bool GstEncoderImpl::encodeFrame(void *t_imgData, size_t t_size)
    {
        // Check img data is ready.
        if (!t_imgData || t_size == 0)
            return false;

        // Make sure that stream is opening.
        if(!m_isStreaming)
            return false;

        // Make sure that appsrc is needing data.
//        std::unique_lock<std::mutex> lock(m_needDataMutex);
//        m_waitAppsrcNeedData.wait(lock, [this](){ return this->m_needData; });
        //if (!m_needData)
        //    m_waitAppsrcNeedData.wait(lock);
        // Create gstbuffer wrapper the given data
        /** ------- Way 1 to create GstBuffer. With this way, when push to appsrc, we need copy to
         *          new allocated memory using gst_buffer_copy.
         */
        /** ------- Way 2 to create GstBuffer */
        GstBuffer* gstImg = gst_buffer_new_allocate(NULL, t_size, NULL);
        if(gstImg == nullptr)
        {
//            printf("gst_buffer_new_allocate fails!\n");
            return false;
        }

//        GstMapInfo map;
//        if (gst_buffer_map(gstImg, &map, GST_MAP_WRITE))
//        {
//            memcpy(map.data, t_imgData, t_size);
//            gst_buffer_unmap(gstImg, &map);
//        }
        GstMapInfo map = {0};
        gst_buffer_map(gstImg, &map, GST_MAP_WRITE);
        memcpy(map.data, t_imgData, t_size);
        gst_buffer_unmap(gstImg, &map);

        // Do set timestamp for buffer
        GstClockTime duration = GST_SECOND / (double) m_encoderOption.capInput.m_framerate;
        GstClockTime timestamp = duration * m_numFrame;
        GST_BUFFER_PTS(gstImg) = timestamp;
        if (!GST_BUFFER_PTS_IS_VALID(gstImg))
            logInfo(GST_ENCODER_LOG, "Gstbuffer failed to setup PTS");

        GST_BUFFER_DTS(gstImg) = timestamp;
        if (!GST_BUFFER_DTS_IS_VALID(gstImg))
            logInfo(GST_ENCODER_LOG, "Gstbuffer failed to setup DTS");

        GST_BUFFER_DURATION(gstImg) = duration;
        if (!GST_BUFFER_DURATION_IS_VALID(gstImg))
            logInfo(GST_ENCODER_LOG, "Gstbuffer failed to setup DURATION");

        GST_BUFFER_OFFSET(gstImg) = m_numFrame++;
        if (!GST_BUFFER_OFFSET_IS_VALID(gstImg))
            logInfo(GST_ENCODER_LOG, "Gstbuffer failed to setup OFFSET");

//        gst_app_src_push_buffer (m_appSrc, gstImg);
        // queue the buffer to gstreamer
        GstFlowReturn ret;
        ret = gst_app_src_push_buffer (m_appSrc, gstImg);
        if (ret != 0)
        {
            logInfo(GST_ENCODER_LOG, "Push gst buffer failed. Error code: %d", ret);
            return false;
        }
//        m_needData = false;
        return true;
    }

    //pause
    void GstEncoderImpl::pause(bool t_pause)
    {
        if(m_gstPipeline == NULL) return;
        gst_element_set_state(GST_ELEMENT(m_gstPipeline), t_pause?GST_STATE_PAUSED:GST_STATE_PLAYING);
    }

    // createCaps
    void GstEncoderImpl::createCaps()
    {
        std::ostringstream ss;
        ss << "video/x-raw";
        ss << ",width=" << m_encoderOption.capInput.m_width;
        ss << ",height=" << m_encoderOption.capInput.m_height;
        ss << ",format=(string)I420";
        ss << ",framerate=" << m_encoderOption.capInput.m_framerate << "/1";
        m_appSrcCapsStr = ss.str();
    }

    // createPipeline
    void GstEncoderImpl::createPipeline()
    {
        std::ostringstream ss;
        ss << "appsrc name=" << m_appSrcName << " is-live=true do-timestamp=true format=3 ! ";
        if (m_encoderOption.codeType <= Gst::Codec::NV_MJPEG)
        {
            ss << "videorate drop-only=true ! nvvidconv ! ";
//            if (m_encoderOption.streamW != m_encoderOption.capInput.m_width ||
//                    m_encoderOption.streamH != m_encoderOption.capInput.m_height ||
//                        m_encoderOption.streamFps != m_encoderOption.capInput.m_framerate)
            if(m_encoderOption.streamW != 0 || m_encoderOption.streamH != 0 || m_encoderOption.streamFps != 0)
            {
                ss << "video/x-raw(memory:NVMM), width=" << (m_encoderOption.streamW != 0 ? m_encoderOption.streamW : m_encoderOption.capInput.m_width) << ", ";
                ss << "height=" << (m_encoderOption.streamH != 0 ? m_encoderOption.streamH : m_encoderOption.capInput.m_height) << ", framerate=";
                ss << (m_encoderOption.streamFps != 0 ? m_encoderOption.streamFps : m_encoderOption.capInput.m_framerate) << "/1 ! ";
            }
        }
        else
        {
            ss << "videoconvert ! ";
            if (m_encoderOption.streamW != 0 || m_encoderOption.streamH != 0)
            {
                ss << "videoscale ! video/x-raw,width=" << m_encoderOption.streamW << ",";
                ss << "height=" << m_encoderOption.streamH << " ! ";
            }

            if (m_encoderOption.streamFps != 0)
            {
                ss << "videorate drop-only=true ! video/x-raw,framerate=";
                ss << m_encoderOption.streamFps << "/1 ! ";
            }
        }

        switch (m_encoderOption.codeType)
        {
            case Gst::Codec::OMX_H265:
                ss << "omxh265enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-h265 ! ";
                break;
            case Gst::Codec::OMX_H264:
                ss << "omxh264enc bitrate=" << m_encoderOption.codingBirtate << " control-rate=2 insert-sps-pps=1 iframeinterval=30 ! video/x-h264 ! ";
                break;
            case Gst::Codec::OMX_VP8:
                ss << "omxvp8enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-vp8 ! ";
                break;
            case Gst::Codec::OMX_VP9:
                ss << "omxvp9enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-vp9 ! ";
                break;
            case Gst::Codec::NVV4L2_H264:
                ss << "nvv4l2h264enc bitrate=" << m_encoderOption.codingBitrate << " insert-sps-pps=1 idrinterval=30 maxperf-enable=true profile=2 ! video/x-h264 ! ";
                break;
            case Gst::Codec::NVV4L2_H265:
                ss << "nvv4l2h265enc bitrate=" << m_encoderOption.codingBitrate << " insert-sps-pps=1 idrinterval=30 maxperf-enable=true ! video/x-h265 ! ";
                break;
            case Gst::Codec::NV_MJPEG:
                ss << "nvjpegenc ! image/jpeg ! ";
                break;
            case Gst::Codec::H265:
                ss << "x265enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-h265 ! ";
                break;
            case Gst::Codec::H264:
                ss << "x264enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-h264 ! ";
                break;
            case Gst::Codec::VP8:
                ss << "vp8enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-vp8 ! ";
                break;
            case Gst::Codec::VP9:
                ss << "vp9enc bitrate=" << m_encoderOption.codingBitrate << " ! video/x-vp9 ! ";
                break;
            case Gst::Codec::MJPEG:
                ss << "jpegenc ! image/jpeg ! ";
                break;
            case Gst::Codec::NV_H265:
                ss << "nvh265enc bitrate=2048000 ! video/x-h265 ! ";
                break;
            case Gst::Codec::NV_H264:
                ss << "nvh264enc bitrate=2048000 ! video/x-h264 ! ";
                break;
        }

        if (m_encoderOption.ipStream != "" && m_encoderOption.outFilename != "")
        {
            if (m_encoderOption.codeType <= Gst::Codec::NV_MJPEG)
            {
                ss << "nvtee ! ";
            }
            else
            {
                ss << "tee ! ";
            }
        }

        if (m_encoderOption.outFilename != "")
        {
            std::string fileExtension = File::fileExtension(m_encoderOption.outFilename);
            if (fileExtension == "mkv")
            {
                ss << "matroskamux ! ";
            }
            else if (fileExtension == "flv")
            {
                ss << "flvmux ! ";
            }
            else if(fileExtension == "avi")
            {
                if (m_encoderOption.codeType == Gst::Codec::OMX_H265
                        || m_encoderOption.codeType == Gst::Codec::OMX_VP9
                            || m_encoderOption.codeType == Gst::Codec::H265
                                || m_encoderOption.codeType == Gst::Codec::VP9
                                    || m_encoderOption.codeType == Gst::Codec::NV_H265)
                {
                    logDanger(GST_ENCODER_LOG, "Avi doesn't support codec H265 and VP9. "
                                                                   "Use: H264, VP8, MJPEG instead");
                    return;
                }
                ss << "avimux ! ";
            }
            else if(fileExtension == "h264")
            {
                ss << "h264parse ! ";
            }
            else if(fileExtension == "h265")
            {
                ss << "h265parse ! ";
            }
            else if (fileExtension == "mp4")
            {
                if (m_encoderOption.codeType == Gst::Codec::OMX_H264
                 || m_encoderOption.codeType == Gst::Codec::H264
                 || m_encoderOption.codeType == Gst::Codec::NV_H264
                 || m_encoderOption.codeType == Gst::Codec::NVV4L2_H264)
                {
                    ss << "h264parse ! ";
                }

                if (m_encoderOption.codeType == Gst::Codec::OMX_H265
                 || m_encoderOption.codeType == Gst::Codec::H265
                 || m_encoderOption.codeType == Gst::Codec::NV_H265
                 || m_encoderOption.codeType == Gst::Codec::NVV4L2_H265)
                {
                    ss << "h265parse ! ";
                }

                ss << "mpegtsmux ! "; // qtmux
            }
            else
            {
                logDanger(GST_ENCODER_LOG, "Not supported " + fileExtension + " file extension."
                                         "Some supported type are: mkv, flv, avi, mp4, H264, H265");
                return;
            }

            ss << "filesink location=" << m_encoderOption.outFilename << " buffer-mode=2 sync=true async=false";
            if (m_encoderOption.ipStream != "")
            {
                ss << " t. ! ";
            }
        }
        
        if (m_encoderOption.ipStream != "")
        {
//            switch (m_encoderOption.codeType)
//            {
//                case Gst::Codec::OMX_H265:
//                case Gst::Codec::H265:
//                case Gst::Codec::NV_H265:
//                    ss << "rtph265pay ";
//                    break;
//                case Gst::Codec::OMX_H264:
//                case Gst::Codec::H264:
//                case Gst::Codec::NV_H264:
//                    ss << "rtph264pay ";
//                    break;
//                case Gst::Codec::OMX_VP8:
//                case Gst::Codec::VP8:
//                    ss << "rtpvp8pay ";
//                    break;
//                case Gst::Codec::OMX_VP9:
//                case Gst::Codec::VP9:
//                    ss << "rtpvp9pay ";
//                    break;
//                case Gst::Codec::NV_MJPEG:
//                case Gst::Codec::MJPEG:
//                    ss << "rtpjpegpay ";
//                    break;
//            }
//            ss << "config-interval = 1 ! udpsink host=" << m_encoderOption.ipStream;
            ss << "mpegtsmux alignment=7 ! rtpmp2tpay name=myPay mtu=4096 ! ";
            ss << "udpsink host=" << m_encoderOption.ipStream;
            ss << " =port" << m_encoderOption.port;
            if (m_encoderOption.autoMulticast)
            {
                ss << " auto-multicast=true";
            }
            ss << " sync=true async=false";
        }

        logInfo(GST_ENCODER_LOG, "Gst encode pipeline: " + ss.str());
        m_gstPipelineStr = ss.str();
    }

    // init
    bool GstEncoderImpl::init()
    {
        createCaps();
        createPipeline();
        GError* err = NULL;
        m_gstPipeline = GST_PIPELINE(gst_parse_launch(m_gstPipelineStr.c_str(), &err));
        if( err != NULL )
        {
            logDanger(GST_ENCODER_LOG, "failed to create pipeline: %s", err->message);
            g_error_free(err);
            return false;
        }

        // Get the appsrc
        GstElement* appSrcEle = gst_bin_get_by_name(GST_BIN(m_gstPipeline), m_appSrcName.c_str());
        m_appSrc = GST_APP_SRC(appSrcEle);
        if (!appSrcEle || !m_appSrc)
        {
            logDanger(GST_ENCODER_LOG, "failed to retrieve appsrc element from pipeline");
            return false;
        }

        // Setup callback
//        GstAppSrcCallbacks cbs;
//        memset(&cbs, 0, sizeof(GstAppSrcCallbacks));
//        cbs.need_data   = onNeedData;
//        cbs.enough_data = onEnoughData;
//        gst_app_src_set_callbacks(m_appSrc, &cbs, (void*)this, NULL);

        // Set caps
        m_appSrcCaps = gst_caps_from_string(m_appSrcCapsStr.c_str());
        gst_app_src_set_caps(m_appSrc, m_appSrcCaps);
        return openStream();
    }

    // onNeedData
    void GstEncoderImpl::onNeedData(GstAppSrc *t_appSrc, uint32_t t_size, void *t_userData)
    {
        if (!t_userData)
            return;

        //logInfo(GST_ENCODER_LOG, "onNeedData -- appsrc requesting data (%u bytes)", t_size);
        GstEncoderImpl* gstEncoder = (GstEncoderImpl* ) t_userData;
//        std::unique_lock<std::mutex> lock(gstEncoder->m_needDataMutex);
        gstEncoder->m_needData = true;
//        lock.unlock();
        gstEncoder->m_waitAppsrcNeedData.notify_one();
    }

    // onEnoughData
    void GstEncoderImpl::onEnoughData(GstAppSrc *t_appSrc, void *t_userData)
    {
        if (!t_userData)
            return;

        //logInfo(GST_ENCODER_LOG, "onEnoughData -- appsrc is busy");
        GstEncoderImpl* gstEncoder = (GstEncoderImpl* ) t_userData;
        gstEncoder->m_needData = false;
    }

    // createEncoderInstance
    GstEncoder* GstEncoder::create(const Option& t_encoderOption)
    {
        if (!Gst::gstreamerInit())
        {
            logDanger(GST_ENCODER_LOG, "failed to initialize gstreamer API.");
            return nullptr;
        }

        GstEncoderImpl* gstEncoder = new GstEncoderImpl(t_encoderOption);
        if (!gstEncoder->init())
        {
            logDanger(GST_ENCODER_LOG, "failed to init gst video encoder pipeline.");
            return nullptr;
        }
        logSuccess(GST_ENCODER_LOG, "init gst video encoder pipeline successfully.");
        return gstEncoder;
    }
}
