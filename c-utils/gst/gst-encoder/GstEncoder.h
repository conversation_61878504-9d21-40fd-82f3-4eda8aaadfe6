/**
 *========================================================================
 * Project: Utils
 * Module: GstEncoder.h
 * Module Short Description:Gstreamer encoder module supports udp streaming
 *                          or video recording features.
 * Author: Trung-Ng (<EMAIL>)
 * Date: 11/06/2020 09:58
 * Organization: Viettel Aerospace Institude - Viettel Group
 *
 * (c) VTX 2020. All Rights reserved.
 * =======================================================================
 */

#ifndef GSTENCODER_H
#define GSTENCODER_H

//---- C++, linux libs
#include <iostream>
#include <sstream>
#include <thread>
#include <condition_variable>

//---- Gstreamer libs
#include <glib.h>
#include <gst/gst.h>
#include <gst/app/gstappsink.h>
#include <gst/app/gstappsrc.h>

//---- Custom libs
#include "../GstUtility.h"

#define GST_ENCODER_LOG "[GstEncoder]"

namespace Utils
{
    /**
     * @brief Video encoder module using gstreamer support video streaming and video recording
     *        features. This module can write data to file with some supported datatypes like
     *        MP4, MKV, AVI or stream video over network using UDP/IP
     *        The supported codecs includes H264, H265, VP8, VP9,...
     */
    class GstEncoder
    {
        //------------------------------------------------------------------------------------------
        //                              Public apis
        //------------------------------------------------------------------------------------------
        public:
            struct _Option {
                Gst::Codec codeType;
                uint32_t codingBitrate;
                Gst::CapOptions capInput;
                uint32_t streamW;
                uint32_t streamH;
                uint32_t streamFps;
                std::string ipStream;
                uint32_t port;
                std::string outFilename;
                std::string outFileMetaname;
                std::string appSrcName;
                bool autoMulticast;

                _Option() {
                    codeType            = Gst::Codec::OMX_H265;
                    codingBitrate       = 4000000;
                    capInput            = Gst::CapOptions();
                    streamW             = 0;
                    streamH             = 0;
                    streamFps           = 0;
                    ipStream            = "";
                    port                = 0;
                    outFilename         = "";
                    autoMulticast       = true;
                    appSrcName          = "";
                }
            };
            typedef struct _Option Option;

            /**
             * @brief create: Create instance of GstEncoder module
             *
             * @param t_encoderOption: List of input option above like codec type, caps option,
             *                         stream over network or record to file ...
             * @return
             */
            static GstEncoder* create(const Option& t_encoderOption);

            /**
             * @brief openStream: Begin running the gstreamer pipeline by translating the state
             *                    to PLAYING. After call the openStream(), encoder module start
             *                    doing encode image frame.
             *
             * @note This api is not strictly necessary to call because we will first check to make
             *       sure that module is already running when we call encodeFrame function. It's
             *       really up to you.
             *
             * @return "true" if success, "false"
             *                  if cannot tranlate gstreamer pipeline state to PLAYING
             */
            virtual bool openStream() = 0;

            /**
             * @brief closeStream: Stop gstreamer api encode frame.
             *
             * @note this api is automatically called by destructor when it gets deleted. So it's
             *       also up to you how to use this api (explicitly or implicitly)
             *
             * @return "true" if success, "false"
             *                  if cannot tranlate gstreamer pipeline state to NULL
             */
            virtual bool closeStream() = 0;

            /**
             * @brief isRunning: Check whether gst stream is running or not.
             *
             * @return "true" if it has already ran, false if otherwise.
             */
            virtual bool isRunning() = 0;

            /**
             * @brief encodeFrame: Feed the data to encoder module.
             *
             * @param t_imgData: the pointer to input image data. The format of stream is
             *                   I420, so we imply that t_imageData input is I420 format already.
             *
             * @return "true" if data is fed into gstreamer pipeline, "false" if the image data
             *          is NULL or gstreamer pipeline haven't needed next image data yet.
             */
            virtual bool encodeFrame(void* t_imgData, size_t t_size) = 0;

            //virtual bool encodeRGBAFrame(void* t_imgData) = 0;

            virtual void pause(bool t_pause) = 0;

        //------------------------------------------------------------------------------------------
        //      xxxxx-- Protected methods (prevent using from other modules or client-- xxxxx
        //------------------------------------------------------------------------------------------
        protected:
            GstEncoder() {} // Prevent directly create GstEncoder.
            ~GstEncoder() {}
            GstEncoder(const GstEncoder& ) = delete; // Prevent create copy constructor.
            GstEncoder& operator=(const GstEncoder& ) = delete; // Prevent assignment operator.
    };
}
#endif // GSTENCODER_H
