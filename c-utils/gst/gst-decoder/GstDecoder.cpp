/**
 *========================================================================
 * Project: Utils
 * Module: GstDecoder
 * Module Short Description: Gstreamer decoder module support decode data
 *        into images and metadata(optional) from UDP or file.
 * Author: Trung-Ng (<EMAIL>)
 * Date: 28/07/2020 15:42
 * Organization: Viettel Aerospace Institude - Viettel Group
 *
 * (c) 2020 VTX. All Rights reserved.
 * =======================================================================
 */

#include "GstDecoder.h"
#include "logging/LogApis.h"
#include "helper/FileHelper.h"
#include "image/ImageFormat.h"
#ifdef NVIDIA
    #include "cuda/CudaMappedMemory.h"
#endif


namespace Utils
{
    class GstDecoderImpl : public GstDecoder
    {
        public:
            GstDecoderImpl(const Option& t_op);
            virtual ~GstDecoderImpl();
            bool openStream() override;
            bool closeStream() override;
            bool isOpening() override;
            bool isEos() override;
            uint32_t outputW() override;
            uint32_t outputH() override;
            uint32_t outputSize() override;
            bool capture(void **t_cpuImg, void **t_gpuImg = nullptr, uint32_t t_imgFormat = 0,
                         uint64_t t_timeout = UINT64_MAX) override;

        //protected:
            bool createPipeline();
            bool init();
            bool inspectInputSrc();
            void queryImageData();

            //--- Gst events
            static void onEOS(GstAppSink* sink, void* user_data);
            static GstFlowReturn onPreroll(GstAppSink* sink, void* user_data);
            static GstFlowReturn onBuffer(GstAppSink* sink, void* user_data);

        private:
            static const uint32_t RINGBUFFER_SIZE = 60;

            Option                  m_options;

            //--- Gst
            std::string             m_pipelineStr;
            GstPipeline*            m_pipeline;
            GstAppSink*             m_appsink;
            bool                    m_newImageSignal;
            bool                    m_isStreaming;
            bool                    m_isEos;

            //--- Image dimension, depth, size
            uint32_t                m_imgWidth;
            uint32_t                m_imgHeight;
            uint32_t                m_imgSize;

            //--- Image ring buffer
            uint32_t                m_lastestRingbufferPos;
            bool                    m_lastestRetrieved;
            void*                   m_imgRingBuffer[RINGBUFFER_SIZE];

            //--- Thread sync
            std::mutex              m_ringMutex;
            std::mutex              m_waitEventMutex;
            std::condition_variable m_waitNewImageEvent;
    };

    //------------------- Public apis
    // GstDecoderImpl
    GstDecoderImpl::GstDecoderImpl(const Option& t_op)
        : m_options(t_op)
    {
        m_pipelineStr = "";
        m_pipeline = nullptr;
        m_appsink = nullptr;
        m_newImageSignal = false;
        m_isStreaming = false;

        m_imgWidth = 0;
        m_imgHeight = 0;
        m_imgSize = 0;

        m_lastestRetrieved = false;
        m_lastestRingbufferPos = 0;
        for (uint32_t i = 0; i < RINGBUFFER_SIZE; i++)
        {
            m_imgRingBuffer[i] = nullptr;
        }
    }

    // ~GstDecoderImpl
    GstDecoderImpl::~GstDecoderImpl()
    {
        closeStream();
        for (uint32_t i = 0; i < RINGBUFFER_SIZE; i++)
        {
            m_imgRingBuffer[i] = nullptr;
        }

        if (m_appsink != NULL)
        {
            gst_object_unref(m_appsink);
            m_appsink = NULL;
        }

        if (m_pipeline != NULL)
        {
            gst_object_unref(m_pipeline);
            m_pipeline = NULL;
        }
    }

    // openStream
    bool GstDecoderImpl::openStream()
    {
        if (m_isStreaming)
            return true;
        logInfo(GST_DECODER_LOG, "transitioning pipeline to GST_STATE_PLAYING");
        const GstStateChangeReturn result = gst_element_set_state(GST_ELEMENT(m_pipeline),
                                                                                GST_STATE_PLAYING);
        if (result != GST_STATE_CHANGE_SUCCESS)
        {
            logWarning(GST_DECODER_LOG, "failed to set decoder pipeline state to PLAYING (error %u)\n",
                                                                                            result);
            return false;
        }
        m_isStreaming = true;
        return true;
    }

    // closeStream
    bool GstDecoderImpl::closeStream()
    {
        if (!m_isStreaming)
            return true;

        logInfo(GST_DECODER_LOG, "transitioning pipeline to GST_STATE_NULL");
        const GstStateChangeReturn result = gst_element_set_state(GST_ELEMENT(m_pipeline),
                                                                                    GST_STATE_NULL);
        if (result != GST_STATE_CHANGE_SUCCESS)
        {
            logWarning(GST_DECODER_LOG, "failed to set pipeline state to PLAYING (error %u)",
                                                                                            result);
            return false;
        }
        return true;
    }

    // isOpening
    bool GstDecoderImpl::isOpening()
    {
        return m_isStreaming;
    }

    // isEos
    bool GstDecoderImpl::isEos()
    {
        return m_isEos;
    }

    // outputW
    uint32_t GstDecoderImpl::outputW()
    {
        return m_imgWidth;
    }

    // outputH
    uint32_t GstDecoderImpl::outputH()
    {
        return m_imgHeight;
    }

    // outputSize
    uint32_t GstDecoderImpl::outputSize()
    {
        return m_imgSize;
    }

    // capture
    bool GstDecoderImpl::capture(void **t_cpuImg, void **t_gpuImg, uint32_t t_imgFormat,
                                                                                 uint64_t t_timeout)
    {
        //Make sure that camera is streaming.
        if (!m_isStreaming && !openStream())
            return false;

        //wait until new image event is fired.
        std::unique_lock<std::mutex> l(m_waitEventMutex);
        m_waitNewImageEvent.wait(l, [this]() {return this->m_newImageSignal;});
        //if (!m_newImageSignal)
        //    m_waitNewImageEvent.wait(l);

        // get lastest ringbuffer position
        m_ringMutex.lock();
        uint32_t lastestPos = m_lastestRingbufferPos;
        bool retrieved = m_lastestRetrieved;
        m_lastestRetrieved = true;
        m_ringMutex.unlock();

        if (retrieved)
            return false;

        if (t_cpuImg != nullptr)
            *t_cpuImg = m_imgRingBuffer[lastestPos];
#ifdef NVIDIA
        if (t_gpuImg != nullptr)
            *t_gpuImg = m_imgRingBuffer[lastestPos];
#endif
        if ((ImageFormat)t_imgFormat != ImageFormat::I420)
        {
            //! TODO
            //! Convert image to new color space.
        }
        return true;
    }

    //-------------- Protected methods
    // createPipeline
    bool GstDecoderImpl::createPipeline()
    {
        std::ostringstream ss;
        if (m_options.uri == Option::URI::FILE)
        {
            ss << "filesrc location=" << m_options.filePath;

            std::string fileExt = File::fileExtension(m_options.filePath);
            if (fileExt == "mkv")
            {
                ss << " ! matroskademux";
            }
            else if (fileExt == "mp4")
            {
                ss << " ! tsdemux";
            }
            else if (fileExt == "qt")
            {
                ss << " ! qtdemux";
            }
            else if (fileExt == "avi")
            {
                ss << " ! avidemux";
            }
            else if (fileExt == "ts")
            {
                ss << " ! tsdemux";
            }
            else if( fileExt != "h264" && fileExt != "h265" )
            {
                logDanger(GST_DECODER_LOG, "gstDecoder -- unsupported video file extension (%s)\n",
                                                                                   fileExt.c_str());
                logDanger(GST_DECODER_LOG, "              supported video extensions are:\n");
                logDanger(GST_DECODER_LOG, "                 * mkv\n");
                logDanger(GST_DECODER_LOG, "                 * mp4, qt\n");
                logDanger(GST_DECODER_LOG, "                 * avi\n");
                logDanger(GST_DECODER_LOG, "                 * h264, h265\n");
                return false;
            }

            ss << " ! queue ";

            if (m_options.codecType == Gst::Codec::H264
                    || m_options.codecType == Gst::Codec::OMX_H264)
            {
                ss << " ! h264parse";
            }
            else if (m_options.codecType == Gst::Codec::H265
                     || m_options.codecType == Gst::Codec::OMX_H265)
            {
                ss << " ! h265parse";
            }
            else if (m_options.codecType == Gst::Codec::MPEG_2
                     || m_options.codecType == Gst::Codec::OMX_MPEG_2 )
            {
                ss << " ! mpegvideoparse";
            }
            else if (m_options.codecType == Gst::Codec::MPEG_4
                     || m_options.codecType == Gst::Codec::OMX_MPEG_4)
            {
                ss << " ! mpeg4videoparse";
            }
        }

        if (m_options.uri == Option::URI::RTP)
        {
            if (m_options.port == 0)
            {
                logDanger(GST_DECODER_LOG, "failed to create decoder pipeline. "
                                           "                            UDP port is not provided.");
                return false;
            }
            ss << "udpsrc port=" << m_options.port;
            if (strcmp(m_options.ipStream, "") != 0)
            {
                ss << " multicast-group=" << m_options.ipStream;
            }

            ss << " ! application/x-rtp,payload=96,clock-rate=90000,encoding-name=";
            if (m_options.codecType == Gst::Codec::H264
                     || m_options.codecType == Gst::Codec::OMX_H264)
            {
                ss << "H264 ! rtph264depay ! h264parse";
            }
            else if (m_options.codecType == Gst::Codec::H265
                     || m_options.codecType == Gst::Codec::OMX_H265)
            {
                ss << "H265 ! rtph265depay ! h265parse";
            }
            else if (m_options.codecType == Gst::Codec::VP8
                     || m_options.codecType == Gst::Codec::OMX_VP8)
            {
                ss << "VP8 ! rtpvp8depay";
            }
            else if (m_options.codecType == Gst::Codec::VP9
                     || m_options.codecType == Gst::Codec::OMX_VP9)
            {
                ss << "VP9 ! rtpvp9depay";
            }
            else if (m_options.codecType == Gst::Codec::MPEG_2
                     || m_options.codecType == Gst::Codec::OMX_MPEG_2)
            {
                ss << "MP2T ! rtpmp2tdepay";		// MP2T-ES
            }
            else if (m_options.codecType == Gst::Codec::MPEG_4
                     || m_options.codecType == Gst::Codec::OMX_MPEG_4)
            {
                ss << "MP4V-ES ! rtpmp4vdepay";	// MPEG4-GENERIC\" ! rtpmp4gdepay
            }
            else if (m_options.codecType == Gst::Codec::MJPEG
                     || m_options.codecType == Gst::Codec::NV_MJPEG)
            {
                ss << "JPEG ! rtpjpegdepay";
            }
        }

        if (m_options.uri == Option::URI::RTSP)
        {
            ss << "rtsp location=" << m_options.rtspLink << " ! queue";
            if (m_options.codecType == Gst::Codec::H264
                    || m_options.codecType == Gst::Codec::OMX_H264)
            {
                ss << " ! rtph264depay ! h264parse";
            }
            else if (m_options.codecType == Gst::Codec::H265
                    || m_options.codecType == Gst::Codec::OMX_H265)
            {
                ss << " ! rtph265depay ! h265parse";
            }
            else if (m_options.codecType == Gst::Codec::VP8
                    || m_options.codecType == Gst::Codec::OMX_VP8)
            {
                ss << " ! rtpvp8depay";
            }
            else if (m_options.codecType == Gst::Codec::H264
                    || m_options.codecType == Gst::Codec::OMX_H264)
            {
                ss << " ! rtpvp9depay";
            }
            else if (m_options.codecType == Gst::Codec::MPEG_2
                    || m_options.codecType == Gst::Codec::OMX_MPEG_2)
            {
                ss << " ! rtpmp2tdepay";		// MP2T-ES
            }
            else if (m_options.codecType == Gst::Codec::MPEG_4
                    || m_options.codecType == Gst::Codec::OMX_MPEG_4)
            {
                ss << " ! rtpmp4vdepay";	// rtpmp4gdepay
            }
            else if (m_options.codecType == Gst::Codec::MJPEG
                    || m_options.codecType == Gst::Codec::NV_MJPEG)
            {
                ss << " ! rtpjpegdepay";
            }
        }

        if (m_options.codecType == Gst::Codec::OMX_H264)
        {
            ss << " ! omxh264dec";
        }
        else if (m_options.codecType == Gst::Codec::OMX_H265)
        {
            ss << " ! omxh265dec";
        }
        else if (m_options.codecType == Gst::Codec::OMX_VP8)
        {
            ss << " ! omxvp8dec";
        }
        else if (m_options.codecType == Gst::Codec::OMX_VP9)
        {
            ss << " ! omxvp9dec";
        }
        else if (m_options.codecType == Gst::Codec::OMX_MPEG_2)
        {
            ss << " ! omxmpeg2videodec";
        }
        else if (m_options.codecType == Gst::Codec::OMX_MPEG_4)
        {
            ss << " ! omxmpeg4videodec";
        }
        else if (m_options.codecType == Gst::Codec::NV_MJPEG)
        {
             ss << " ! nvjpegdec";
        }
        else if (m_options.codecType == Gst::Codec::H264)
        {
            ss << " ! avdec_h264";
        }
        else if (m_options.codecType == Gst::Codec::H265)
        {
            ss << " ! avdec_h265";
        }
        else if (m_options.codecType == Gst::Codec::VP8)
        {
            ss << " ! vp8dec";
        }
        else if (m_options.codecType == Gst::Codec::VP9)
        {
            ss << " ! vp9dec";
        }
        else if (m_options.codecType == Gst::Codec::MPEG_2)
        {
            ss << " ! avdec_mpeg2video";
        }
        else if (m_options.codecType == Gst::Codec::MPEG_4)
        {
            ss << " ! avdec_mpeg4";
        }
        else if (m_options.codecType == Gst::Codec::MJPEG)
        {
            ss << " ! jpegdec";
        }

        if (m_options.codecType >= Gst::Codec::H264)
        {
            ss << " ! videoconvert";
        }
        else
        {
            ss << " ! nvvidconv";
        }

        if (m_options.flipMethod != Option::FlipMethod::FLIP_NONE)
        {
            ss << " ! videoflip method=" << (int) m_options.flipMethod;
        }

        if (m_options.capOption.m_width > 0 || m_options.capOption.m_height > 0)
        {
            ss << " ! videoscale ! video/x-raw";
            if (m_options.capOption.m_width > 0)
            {
                ss << ", width=(int)" << m_options.capOption.m_width;
            }
            if (m_options.capOption.m_height > 0)
            {
                ss << ", height=(int)" << m_options.capOption.m_height;
            }
        }

        if (m_options.capOption.m_framerate > 0)
        {
            ss << " ! videorate ! video/x-raw,framerate=" << m_options.capOption.m_framerate << "/1";
        }
        ss << " ! appsink name=mySink";

        m_pipelineStr = ss.str();
        logInfo(GST_DECODER_LOG, "decoder pipeline: %s", m_pipelineStr.c_str());
        return true;
    }

    // init
    bool GstDecoderImpl::init()
    {
        if (!inspectInputSrc())
            return false;

        if (!createPipeline())
            return false;

        GError* err = NULL;
        m_pipeline = GST_PIPELINE(gst_parse_launch(m_pipelineStr.c_str(), &err));
        if( err != NULL )
        {
            logWarning(GST_DECODER_LOG, "gstdecoder failed to create pipeline "
                                           "at %d - %s - (%s)\n", __LINE__, __FILE__, err->message);
            g_error_free(err);
            return false;
        }

        //Get the appsink
        GstElement* appSinkEle = gst_bin_get_by_name(GST_BIN(m_pipeline), "mySink");
        m_appsink = GST_APP_SINK(appSinkEle);
        if (!appSinkEle || !m_appsink)
        {
            logWarning(GST_DECODER_LOG, "gstdecoder failed to retrieve appsink element "
                                        "from pipeline");
            return false;
        }

        //Setup callback
        GstAppSinkCallbacks cbs;
        memset(&cbs, 0, sizeof(GstAppSinkCallbacks));
        cbs.eos = onEOS;
        cbs.new_preroll = onPreroll;
        cbs.new_sample = onBuffer;
        gst_app_sink_set_callbacks(m_appsink, &cbs, (void*)this, NULL);

        return true;
    }

    // inspectInputSrc
    bool GstDecoderImpl::inspectInputSrc()
    {
        // RTP streams can't be discovered
        if (m_options.uri == Option::URI::RTP)
            return true;

        // create a new discovery interface
        GError* err = NULL;
        GstDiscoverer* discoverer = gst_discoverer_new(5 * GST_SECOND, &err);

        if (!discoverer)
        {
            logWarning(GST_DECODER_LOG, " failed to create gstreamer discovery "
                                        "                           instance:  %s\n", err->message);
            return false;
        }

        if (m_options.uri == Option::URI::FILE && !File::fileExists(m_options.filePath))
        {
            logDanger(GST_DECODER_LOG, "failed to created decoder pipeline.File is not exist!");
            return false;
        }
        GstDiscovererInfo* info = gst_discoverer_discover_uri(discoverer,
                m_options.uri == Option::URI::FILE
                                ? std::string("file://" + std::string(m_options.filePath)).c_str()
                                : m_options.rtspLink, &err);

        if (!info || err != NULL)
        {
            logWarning(GST_DECODER_LOG, "inspect input src failed. %s\n", err->message);
            return false;
        }

        GstDiscovererStreamInfo* rootStream = gst_discoverer_info_get_stream_info(info);

        if (!rootStream)
        {
            logWarning(GST_DECODER_LOG, "failed to discover stream info\n");
            return false;
        }

        GstDiscovererVideoInfo* videoInfo = Gst::findVideoStreamInfo(rootStream);
        GstDiscovererStreamInfo* streamInfo = GST_DISCOVERER_STREAM_INFO(videoInfo);

        if (!videoInfo)
        {
            logWarning(GST_DECODER_LOG, "failed to discover any video streams\n");
            return false;
        }

        // retrieve video resolution
        const guint width  = gst_discoverer_video_info_get_width(videoInfo);
        const guint height = gst_discoverer_video_info_get_height(videoInfo);

        const float framerate_num   = gst_discoverer_video_info_get_framerate_num(videoInfo);
        const float framerate_denom = gst_discoverer_video_info_get_framerate_denom(videoInfo);
        const float framerate       = framerate_num / framerate_denom;

        logInfo(GST_DECODER_LOG, "gstDecoder -- discovered video resolution: %ux%u  (framerate %f Hz)\n",
                                                                          width, height, framerate);

        m_imgWidth = width;
        m_imgHeight = height;

        if (m_options.capOption.m_width == width)
        {
            m_options.capOption.m_width = 0;
        }

        if (m_options.capOption.m_height == height)
        {
            m_options.capOption.m_height = 0;
        }

        if (m_options.capOption.m_framerate == framerate)
        {
            m_options.capOption.m_framerate = 0;
        }

        // retrieve video caps
        GstCaps* caps = gst_discoverer_stream_info_get_caps(streamInfo);

        if (!caps)
        {
            logWarning(GST_DECODER_LOG, "failed to discover video caps\n");
            return false;
        }

        const std::string videoCaps = gst_caps_to_string(caps);

        logInfo(GST_DECODER_LOG, "discovered video caps:  %s\n", videoCaps.c_str());

        // parse codec
        if (videoCaps.find("video/x-h264") != std::string::npos)
        {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::OMX_H264;
#else
            m_options.codecType = Gst::Codec::H264;
#endif
        }
        else if (videoCaps.find("video/x-h265") != std::string::npos)
        {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::OMX_H265;
#else
            m_options.codecType = Gst::Codec::H265;
#endif
        }
        else if (videoCaps.find("video/x-vp8") != std::string::npos)
        {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::OMX_VP8;
#else
            m_options.codecType = Gst::Codec::VP8;
#endif
        }
        else if (videoCaps.find("video/x-vp9") != std::string::npos)
        {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::OMX_VP9;
#else
            m_options.codecType = Gst::Codec::VP9;
#endif
        }
        else if( videoCaps.find("image/jpeg") != std::string::npos )
        {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::NV_MJPEG;
#else
            m_options.codecType = Gst::Codec::MJPEG;
#endif
        }
        else if( videoCaps.find("video/mpeg") != std::string::npos )
        {
            if( videoCaps.find("mpegversion=(int)4") != std::string::npos )
            {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::OMX_MPEG_4;
#else
            m_options.codecType = Gst::Codec::MPEG_4;
#endif
            }
            else if( videoCaps.find("mpegversion=(int)2") != std::string::npos )
            {
#ifdef NVIDIA
            m_options.codecType = Gst::Codec::OMX_MPEG_2;
#else
            m_options.codecType = Gst::Codec::MPEG_2;
#endif
            }
        }
        else
        {
            logDanger(GST_DECODER_LOG, "gstDecoder -- unsupported codec, supported codecs are:\n");
            logDanger(GST_DECODER_LOG, "                 * h264\n");
            logDanger(GST_DECODER_LOG, "                 * h265\n");
            logDanger(GST_DECODER_LOG, "                 * vp8\n");
            logDanger(GST_DECODER_LOG, "                 * vp9\n");
            logDanger(GST_DECODER_LOG, "                 * mpeg2\n");
            logDanger(GST_DECODER_LOG, "                 * mpeg4\n");
            logDanger(GST_DECODER_LOG, "                 * mjpeg\n");

            return false;
        }

        //gst_discoverer_stop(discoverer);
        gst_caps_unref(caps);
        gst_discoverer_stream_info_unref(streamInfo);
        gst_discoverer_stream_info_unref(rootStream);
        g_object_unref(discoverer);
        return true;
    }

    // queryImageData
    void GstDecoderImpl::queryImageData()
    {
        if (!m_appsink)
            return;
        GstSample* gstSample = gst_app_sink_pull_sample(m_appsink);
        if (!gstSample)
        {
            //printf(GST_READER_LOG "gst_app_sink_pull_sample return NULL\n");
            return;
        }
        GstBuffer* gstBuffer = gst_sample_get_buffer(gstSample);
        if (!gstBuffer)
        {
            //printf(GST_READER_LOG "gst_sample_get_buffer return NULL\n");
            return;
        }

        // retrieve
        GstMapInfo map;
        if (!gst_buffer_map(gstBuffer, &map, GST_MAP_READ))
        {
            //printf(GST_READER_LOG " gst_buffer_map failed...\n");
            return;
        }

        //gst_util_dump_mem(map.data, map.size);

        void* gstData = map.data; //GST_BUFFER_DATA(gstBuffer);
        const uint32_t gstSize = map.size; //GST_BUFFER_SIZE(gstBuffer);

        if (!gstData)
        {
            //printf(GST_READER_LOG "gst_buffer had NULL data pointer...\n");
            gst_sample_unref(gstSample);
            return;
        }

        // retrieve caps
        GstCaps* gstCaps = gst_sample_get_caps(gstSample);

        if (!gstCaps)
        {
            //printf(GST_READER_LOG "gst_buffer had NULL caps...\n");
            gst_sample_unref(gstSample);
            return;
        }

        GstStructure* gstCapsStruct = gst_caps_get_structure(gstCaps, 0);

        if (!gstCapsStruct)
        {
            //printf(GST_READER_LOG "gst_caps had NULL structure...\n");
            gst_sample_unref(gstSample);
            return;
        }

        // get width & height of the buffer
        int width  = 0;
        int height = 0;

        if (!gst_structure_get_int(gstCapsStruct, "width", &width) ||
            !gst_structure_get_int(gstCapsStruct, "height", &height))
        {
            //printf(GST_READER_LOG "gst_caps missing width/height...\n");
            gst_sample_unref(gstSample);
            return;
        }

        if (width < 1 || height < 1)
        {
            gst_sample_unref(gstSample);
            return;
        }

        m_imgWidth  = width;
        m_imgHeight = height;
        m_imgSize   = gstSize
                ;
#ifdef NVIDIA
        if (!m_imgRingBuffer[0])
        {
            for (uint32_t i = 0; i < RINGBUFFER_SIZE; i++)
            {
                if (!cudaAllocMapped(&m_imgRingBuffer[i], gstSize))
                {
                    logWarning(GST_DECODER_LOG, "failed to allocate ringbuffer %u  (size=%u)\n",
                                                                                        i, gstSize);
                }
            }
        }
#endif
        const uint32_t nextRingBufferPos = (m_lastestRingbufferPos + 1) % RINGBUFFER_SIZE;
        //memcpy(m_imgRingBuffer[nextRingBufferPos], gstData, gstSize);
        m_imgRingBuffer[nextRingBufferPos] = gstData;
        gst_buffer_unmap(gstBuffer, &map);
        gst_sample_unref(gstSample);

        m_ringMutex.lock();
        m_lastestRingbufferPos = nextRingBufferPos;
        m_lastestRetrieved = false;
        m_ringMutex.unlock();
        m_waitNewImageEvent.notify_all();
    }

    // onEOS event
    void GstDecoderImpl::onEOS(GstAppSink *sink, void *user_data)
    {
        logWarning(GST_DECODER_LOG, "gstDecoder -- end of stream (EOS)\n");

        if( !user_data )
            return;

        GstDecoderImpl* dec = (GstDecoderImpl*)user_data;

        dec->m_isEos = true;
    }

    // onPreroll Event
    GstFlowReturn GstDecoderImpl::onPreroll(GstAppSink *sink, void *user_data)
    {
        printf(GST_DECODER_LOG " onPreroll event\n");
        if( !user_data )
            return GST_FLOW_OK;

        GstDecoderImpl* dec = (GstDecoderImpl*)user_data;

        // onPreroll gets called sometimes, just pull and free the buffer
        // otherwise the pipeline may hang during shutdown
        GstSample* gstSample = gst_app_sink_pull_preroll(dec->m_appsink);

        if (!gstSample)
        {
            logWarning(GST_DECODER_LOG, "app_sink_pull_sample() returned NULL...\n");
            return GST_FLOW_OK;
        }

        gst_sample_unref(gstSample);
        return GST_FLOW_OK;
    }

    // onBuffer event
    GstFlowReturn GstDecoderImpl::onBuffer(GstAppSink *sink, void *user_data)
    {
        if (!user_data)
            return GST_FLOW_OK;
        printf(GST_DECODER_LOG " onBuffer event\n");
        GstDecoderImpl* gstReader = (GstDecoderImpl*) user_data;
        gstReader->m_newImageSignal = true;
        gstReader->queryImageData();
        return GST_FLOW_OK;
    }

    // create decoder instance
    GstDecoder* GstDecoder::createInst(const char *t_ipStream, const uint32_t t_port,
                                       const Gst::Codec &t_codec, const uint32_t &t_customImW,
                                       const uint32_t &t_customImH, const uint32_t &t_customFps,
                                       Option::FlipMethod t_flipMethod)
    {

        if (!Gst::gstreamerInit())
        {
            logDanger(GST_DECODER_LOG, "failed to initialize gstreamer API.");
            return nullptr;
        }

        Option decoderOption;
        decoderOption.uri = Option::URI::RTP;
        decoderOption.ipStream = t_ipStream;
        decoderOption.port = t_port;
        decoderOption.codecType = t_codec;
        decoderOption.capOption.m_width = t_customImW;
        decoderOption.capOption.m_height = t_customImH;
        decoderOption.capOption.m_framerate = t_customFps;
        decoderOption.flipMethod = t_flipMethod;
        GstDecoderImpl* inst = new GstDecoderImpl(decoderOption);

        if (!inst->init())
        {
            logDanger(GST_DECODER_LOG, "failed to init gst video  pipeline.");
            return nullptr;
        }
        logSuccess(GST_DECODER_LOG, "init gst video decoder pipeline successfully.");
        return inst;
    }

    // create decoder instance
    GstDecoder* GstDecoder::createInst(const char *t_inputLink,
                                       const uint32_t &t_customImW, const uint32_t &t_customImH,
                                       const uint32_t &t_customFps, Option::FlipMethod t_flipMethod)
    {
        if (!Gst::gstreamerInit())
        {
            logDanger(GST_DECODER_LOG, "failed to initialize gstreamer API.");
            return nullptr;
        }

        Option decoderOption;
        if (strstr(t_inputLink, "rtsp"))
        {
            decoderOption.uri = Option::URI::RTSP;
            decoderOption.rtspLink = t_inputLink;
        }
        else
        {
            decoderOption.uri = Option::URI::FILE;
            decoderOption.filePath = t_inputLink;
        }
        decoderOption.capOption.m_width = t_customImW;
        decoderOption.capOption.m_height = t_customImH;
        decoderOption.capOption.m_framerate = t_customFps;
        decoderOption.flipMethod = t_flipMethod;
        GstDecoderImpl* inst = new GstDecoderImpl(decoderOption);

        if (!inst->init())
        {
            logDanger(GST_DECODER_LOG, "failed to init gst video  pipeline.");
            return nullptr;
        }
        logSuccess(GST_DECODER_LOG, "init gst video decoder pipeline successfully.");
        return inst;
    }
}
