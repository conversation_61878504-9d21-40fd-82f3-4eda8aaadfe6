/**
 *========================================================================
 * Project: Utils
 * Module: GstDecoder
 * Module Short Description: Gstreamer decoder module support decode data
 *        into images and metadata(optional) from UDP or file.
 * Author: Trung-Ng (<EMAIL>)
 * Date: 28/07/2020 15:42
 * Organization: Viettel Aerospace Institude - Viettel Group
 *
 * (c) 2020 VTX. All Rights reserved.
 * =======================================================================
 */

#ifndef GSTDECODER_H
#define GSTDECODER_H

//---- C++, linux libs
#include <iostream>
#include <sstream>
#include <thread>
#include <condition_variable>

//---- Gstreamer libs
#include <glib.h>
#include <gst/gst.h>
#include <gst/app/gstappsink.h>
#include <gst/app/gstappsrc.h>
#include <gst/pbutils/pbutils.h>

//---- Custom libs
#include "../GstUtility.h"

#define GST_DECODER_LOG "[GstDecoder]"
namespace Utils
{
    /**
     * @brief Video decoder module: supports decode video streaming RTP/RTSP over network
     *        or decode video file from disk (MKV, MP4, FLV, AVI, ...). The supported decoder codecs
     *        are H264, H265, VP8, VP9, MPEG-4, MJPEG,...
     *        This module does reversely encoder module, translate from encode data to image.
     */
    class GstDecoder
    {
        public:
            /**
             * @brief The _Option struct: Define input option for decoder module.
             */
            struct _Option {
                // what to be decoded.
                enum class URI
                {
                    FILE,
                    RTP,
                    RTSP
                };
                URI uri;

                enum class FlipMethod
                {
                    FLIP_NONE = 0, // No rotation
                    CLOCKWISE,     // Rotate clockwise 90 degrees.
                    RORATE_180,    // Rotate 180 degrees
                    COUNTER_CLOCKWISE, // Rotate counter-clockwise 90 degrees.
                    HORIZONTAL_FLIP, // Flip horizontally
                    VERTICAL_FLIP, // Flip vertically
                    UPPER_LEFT_DIAGONAL, // Flip across upper left/lower right diagonal
                    UPPER_RIGHT_DIAGONAL // Flip across upper right/lower left diagonal
                };

                FlipMethod flipMethod;

                // Codec
                Gst::Codec codecType;

                // Custom width, height, framerate
                Gst::CapOptions capOption;

                // RTP network streaming infos
                const char* ipStream;
                uint32_t port;
                bool autoMulticast;

                // RTSP info
                const char* rtspLink;

                // File path
                const char* filePath;

                _Option()
                {
                    uri = URI::RTP;
                    flipMethod = FlipMethod::FLIP_NONE;
                    codecType = Gst::Codec::H265;
                    capOption.m_width = 0;
                    capOption.m_height = 0;
                    capOption.m_framerate = 0;
                    ipStream = "";
                    port = 0;
                    autoMulticast = true;
                    rtspLink = "";
                    filePath = "";
                }
            };
            typedef struct _Option Option;

            /**
             * @brief createInst: Create gst decoder instance to decode rtp stream
             * @param[in] t_ipStream: The multicast group or null if it is streamed directly
             *                        to this decoder device.
             * @param[in] t_port: receive port
             * @param[in] t_codec: codec type
             * @param[in] t_customImW: Custom output image with. If it is set 0, use default
             *                         stream with
             * @param[in] t_customImH: Custom output image height. If it is set 0, use default
             *                         stream height
             * @param[in] t_customFps: Custom output image framerate. If it is set 0, use default
             *                         stream framerate
             * @return
             */
            static GstDecoder* createInst(const char* t_ipStream, const uint32_t t_port,
                                   const Gst::Codec& t_codec, const uint32_t& t_customImW = 0,
                                   const uint32_t& t_customImH = 0, const uint32_t& t_customFps = 0,
                                   Option::FlipMethod t_flipMethod = Option::FlipMethod::FLIP_NONE);

            /**
             * @brief createInst: Create gst decoder instance to decode file or rtsp stream.
             * @param t_inputLink: File path / rtsp server link.
             * @param[in] t_customImW: Custom output image with. If it is set 0, use default
             *                         stream with
             * @param[in] t_customImH: Custom output image height. If it is set 0, use default
             *                         stream height
             * @param[in] t_customFps: Custom output image framerate. If it is set 0, use default
             *                         stream framerate
             * @return
             */
            static GstDecoder* createInst(const char* t_inputLink, const uint32_t& t_customImW = 0,
                                   const uint32_t& t_customImH = 0, const uint32_t& t_customFps = 0,
                                   Option::FlipMethod t_flipMethod = Option::FlipMethod::FLIP_NONE);

            /**
             * @brief openStream: Open stream to start decoding input source.
             * @return
             */
            virtual bool openStream() = 0;

            /**
             * @brief closeStream: Stop and close decoding task.
             * @return
             */
            virtual bool closeStream() = 0;

            /**
             * @brief isOpening: Check if decoder is working or not.
             * @return
             */
            virtual bool isOpening() = 0;

            /**
             * @brief isEos: Check if decoder is completed decode input source or not.
             * @return
             */
            virtual bool isEos() = 0;

            /**
             * @brief outputW: Get output decoded stream with.
             * @return
             */
            virtual uint32_t outputW() = 0;

            /**
             * @brief outputH: Get output decoded stream height
             * @return
             */
            virtual uint32_t outputH() = 0;

            /**
             * @brief outputSize: Get output decoded stream size
             * @return
             */
            virtual uint32_t outputSize() = 0;

            /**
             * @brief capture: Get out output image from decoding stream.
             * @param t_cpuImg: cpu pointer to output image memory
             * @param t_gpuImg: gpu pointer to output image memory
             * @param t_imgFormat: Custom ouput format (value is cast to ImageFormat enum)
             * @param t_timeout
             * @return
             */
            virtual bool capture(void** t_cpuImg, void** t_gpuImg = nullptr, uint32_t t_imgFormat = 0,
                         uint64_t t_timeout = UINT64_MAX) = 0;


        protected:
            GstDecoder(const GstDecoder& ) = delete;
            GstDecoder& operator=(const GstDecoder& ) = delete;
            GstDecoder() {}
            ~GstDecoder() {}

    };
}
#endif // GSTDECODER_H
