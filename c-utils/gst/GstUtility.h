/**
 *========================================================================
 * Project: Utils
 * Module: GstUtility.h
 * Module Short Description:
 * Author: Trung-Ng (<EMAIL>)
 * Date: 20/05/2020 16:41
 * Organization: Viettel Aerospace Institude - Viettel Group
 *
 * (c) 2020 VTX. All Rights reserved.
 * =======================================================================
 */

#ifndef __GSTREAMER_UTILITY_H__
#define __GSTREAMER_UTILITY_H__

#include <gst/gst.h>
#include <gst/pbutils/pbutils.h>
#include <string>


namespace Utils {
    namespace Gst {
        /**
         * @brief The Codec enum: Video codec types.
         */
        enum class Codec {
            /**
             * Codec types on Nvidia devices. If one of those in list c
             */
            OMX_H264 = 0, /**< omx264enc or omxh264dec */
            OMX_H265, /**< omxh265enc or omxh265dec */
            OMX_VP8,  /**< omxvp8enc or omxvp8dec */
            OMX_VP9,  /**< omxvp9enc or omxvp9dec */
            NVV4L2_H264, /**< nvv4l2h264enc */
            NVV4L2_H265, /**< nvv4l2h265enc */
            NV_MJPEG, /**< nvjpegenc or nvjpegdec */
            OMX_MPEG_2, /**< omxmpeg2videodec */
            OMX_MPEG_4, /**< omxmpeg4videodec */

            /**
             * Codec types on CPU device
             */
            H264,    /**< x264enc or avdec_h264 */
            H265,    /**< x265enc or avdec_h265 */
            VP8,     /**< vp8enc or vp8dec  */
            VP9,     /**< vp9enc or vp9 */
            MJPEG,   /**< jpegenc or jpegdec  */
            MPEG_2,  /**< mpeg2dec for decode only */
            MPEG_4,  /**< avdec_mpeg4 for decode only */

            NV_H265, /**< nvh265enc or nvh265dec */
            NV_H264  /**< nvh264enc or nvh264dec */
        };

        /**
         * @brief codecStrToEnum: Convert codec str to enum
         * @param t_codecStr
         * @return
         */
        inline Codec codecStrToEnum(std::string& t_codecStr)
        {
            if (t_codecStr == "omx_h264")
                return Codec::OMX_H264;
            if (t_codecStr == "omx_h265")
                return Codec::OMX_H265;
            if (t_codecStr == "omx_vp8")
                return Codec::OMX_VP8;
            if (t_codecStr == "omx_vp9")
                return Codec::OMX_VP9;
            if (t_codecStr == "nvv4l2_h264")
                return Codec::NVV4L2_H264;
            if (t_codecStr == "nvv4l2_h265")
                return Codec::NVV4L2_H265;
            if (t_codecStr == "nv_mjpeg")
                return Codec::NV_MJPEG;
            if (t_codecStr == "h264")
                return Codec::H264;
            if (t_codecStr == "h265")
                return Codec::H265;
            if (t_codecStr == "vp8")
                return Codec::VP8;
            if (t_codecStr == "vp9")
                return Codec::VP9;
            if (t_codecStr == "mjpeg")
                return Codec::MJPEG;
            t_codecStr = "omx_h265";
            return Codec::OMX_H265;
        }

        /**
         * @brief The URI enum: Encode to file/ decode from file or encode over network / decode
         *                      from network streaming.
         */
        enum class URI {
            FILE,   /**< Encode to file or decode from file */
            RTP     /**< Streaming over network */
        };

        /**
         * @brief The CapOptions struct: List constrainst options for encoding input frame.
         *                               Default is fullHD, 30FPS
         */
        struct CapOptions {
            uint32_t m_width = 1920;    /**< The width of the stream (in pixels). Default is 1920 */
            uint32_t m_height = 1080;   /**< The height of the stream (in pixels). Default is 1080 */
            uint32_t m_framerate = 30;   /**< The framerate of the stream. Default is 30Hz */
        };

        /**
         * LOG_GSTREAMER logging prefix
         * @ingroup codec
         */
        #define LOG_GSTREAMER "[gstreamer] "


        /**
         * gstreamerInit: initialized gstreamer API
         * @internal
         * @ingroup codec
         */
        bool gstreamerInit();

        /**
         * gst_message_print
         * @internal
         * @ingroup codec
         */
        gboolean gst_message_print(_GstBus* t_bus, _GstMessage* t_message, void* t_user_data);

        /**
         * @brief findVideoStreamInfo
         * @param info
         * @return
         */
        GstDiscovererVideoInfo* findVideoStreamInfo(GstDiscovererStreamInfo* t_info);
    }

}
#endif

